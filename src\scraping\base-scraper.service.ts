import { Injectable, Logger } from '@nestjs/common';
import * as puppeteer from 'puppeteer';
import { <PERSON><PERSON><PERSON>, HTTPRequest, Page } from 'puppeteer';
import {
  ProductSelectors,
  Retryoptions,
  ScrapeProductData,
} from '../interfaces/scraper.interface';

@Injectable()
export abstract class BaseScraperService {
  protected readonly logger = new Logger(this.constructor.name);
  protected browser: <PERSON><PERSON><PERSON>;

  protected async initBrowser(): Promise<void> {
    if (this.browser) return;

    try {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-first-run',
          '--disable-gpu',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--disabe-web-security',
          '--disable-infobars',
        ],
      });
      this.logger.log('Browser initialised');
    } catch (error) {
      this.logger.error('Failed to initialise browser', error);
      throw error;
    }
  }

  protected async createPage(): Promise<Page> {
    await this.initBrowser();

    const page = await this.browser.newPage();
    await page.setViewport({
      width: 1920,
      height: 1080,
    });
    await page.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.114 Safari/537.36',
    );
    await page.setRequestInterception(true);
    page.on('request', (req: HTTPRequest) => {
      const resourceType = req.resourceType();
      if (
        resourceType === 'stylesheet' ||
        resourceType === 'font' ||
        resourceType === 'image'
      ) {
        void req.abort();
      } else {
        void req.continue();
      }
    });

    page.setDefaultTimeout(300000);
    page.setDefaultNavigationTimeout(300000);

    return page;
  }

  protected async withRetry<T>(
    operation: () => Promise<T>,
    options: Retryoptions,
    context: string,
  ): Promise<T> {
    let lastError: Error = new Error('Fallback');

    for (let attempt = 1; attempt <= options.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        this.logger.warn(
          `${context} - Attempt ${attempt}/${options.maxRetries} failed:`,
          lastError.message,
        );

        if (attempt < options.maxRetries) {
          const delay = options.delay * Math.pow(2, attempt - 1);
          this.logger.debug(`Retrying in ${delay}ms`);
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }
    this.logger.error(`${context} - All attempts failed`, lastError.stack);
    throw lastError;
  }

  protected async navigateUrl(page: Page, url: string): Promise<void> {
    const retryOptions = {
      maxRetries: 3,
      delay: 3000,
    };

    await this.withRetry(
      async () => {
        this.logger.debug(`Navigating to ${url}`);

        await page.goto(url, {
          waitUntil: 'networkidle2',
          timeout: 300000,
        });
      },
      retryOptions,
      `Failed to navigate to ${url}`,
    );
  }

  protected async waitForElement(
    page: Page,
    selector: string,
    timeout: number = 300000,
  ): Promise<void> {
    try {
      await page.waitForSelector(selector, { timeout });

      this.logger.debug(`Element found: ${selector}`);
    } catch (error) {
      this.logger.warn(`Element not found: ${selector}`);
      throw new Error(`Element not found: ${selector}`);
    }
  }

  protected async extractText(
    page: Page,
    selector: string,
  ): Promise<string | null> {
    try {
      const element = await page.$(selector);
      if (!element) {
        this.logger.debug(`Element not found for text extraction: ${selector}`);
        return null;
      }
      const text = await page.evaluate(
        (element) => element?.textContent?.trim() || '',
        element,
      );
      return text || null;
    } catch (error) {
      const thisError = error as Error;
      this.logger.warn(
        `Failed to extract text from ${selector}`,
        thisError.message,
      );
      return null;
    }
  }

  protected async extractAttribute(
    page: Page,
    selector: string,
    attribute: string,
  ): Promise<string | null> {
    try {
      const element = await page.$(selector);
      if (!element) {
        this.logger.debug(
          `Element not found for attribute extraction: ${selector}`,
        );
        return null;
      }

      const value = await page.evaluate(
        (element, attribute) => element?.getAttribute(attribute) || '',
        element,
        attribute,
      );
      return value || null;
    } catch (error) {
      this.logger.warn(
        `Failed to extract attribute ${attribute} from ${selector}`,
        error.message,
      );
      return null;
    }
  }

  protected parsePrice(priceString: string): number | null {
    if (!priceString) return null;

    const cleanPrice = priceString
      .replace(/[\$£€¥₹,\s]/g, '')
      .replace(/[^\d.]/g, '');

    const price = parseFloat(cleanPrice);
    return isNaN(price) ? null : price;
  }

  protected cleanTitle(title: string): string {
    if (!title) return '';

    return title
      .trim()
      .trim()
      .replace(/\s+/g, '')
      .replace(/[^\w\s-]/g, '')
      .substring(0, 255);
  }

  // this implementation is very basic and needs to be addressed.
  // have to automate captcha solving probably with 2Captcha
  // need to address Cloudfare and js guards
  protected async handleAntiBot(page: Page): Promise<void> {
    try {
      await this.randomDelay(1000, 3000);

      const captchaSelectors = [
        '[data-cy="captcha"]',
        '.captcha',
        '#captcha',
        '[src*="captcha"]',
        'iframe[src*="recaptcha"]',
      ];

      for (const selector of captchaSelectors) {
        const captchaElement = await page.$(selector);
        if (captchaElement) {
          this.logger.warn('Captcha detected, waiting for manual resolution');
          await this.sleep(10000);
          break;
        }
      }
      await this.simulateHumanScroll(page);
    } catch (error) {
      this.logger.warn('Failed to handle anti-bot measures', error.message);
    }
  }

  protected async randomDelay(min: number, max: number): Promise<void> {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    await new Promise((resolve) => setTimeout(resolve, delay));
  }

  protected async sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  protected async simulateHumanScroll(page: Page): Promise<void> {
    try {
      const scrollSteps = Math.floor(Math.random() * 3) + 2; // 2-4 scroll steps
      const viewportHeight = await page.evaluate(() => window.innerHeight);

      for (let i = 0; i < scrollSteps; i++) {
        const scrollY = (viewportHeight / scrollSteps) * (i + 1);
        await page.evaluate((y) => window.scrollTo(0, y), scrollY);
        await this.randomDelay(500, 1500);
      }

      await page.evaluate(() => window.scrollTo(0, 0));
      await this.randomDelay(500, 1000);
    } catch (error) {
      this.logger.debug('Error during scroll simulation:', error.message);
    }
  }

  protected async closePage(page: Page): Promise<void> {
    try {
      if (page && !page.isClosed()) {
        await page.close();
      }
    } catch (error) {
      this.logger.warn('Failed to close page', error.message);
    }
  }

  async onModuleDestroy(): Promise<void> {
    try {
      if (this.browser) {
        await this.browser.close();
        this.logger.log('Browser closed');
      }
    } catch (error) {
      this.logger.error('Failed to close browser', error.message);
    }
  }

  abstract scrapeProduct(
    url: string,
  ): Promise<ScrapeProductData | ScrapeProductData[]>;
  protected abstract getSelectors(): ProductSelectors;
  protected abstract canScrape(url: string): boolean;
}
