import { Test, TestingModule } from '@nestjs/testing';
import { ScrappingController } from './scraping.controller';
import { ScrappingService } from './scraping.service';

describe('ScrappingController', () => {
  let controller: ScrappingController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ScrappingController],
      providers: [ScrappingService],
    }).compile();

    controller = module.get<ScrappingController>(ScrappingController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
