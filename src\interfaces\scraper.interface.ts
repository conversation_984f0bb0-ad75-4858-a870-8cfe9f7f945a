export interface Retryoptions {
  maxRetries: number;
  delay: number;
}

export interface ScrapeProductData {
  title: string;
  price: number | null;
  originalPrice?: number | null;
  currency: string;
  availability: ProductAvailability;
  imageUrl?: string;
  rating?: number | null;
  reviewCount?: number | null;
  seller?: string;
  brand?: string;
  description?: string;
  features?: string[];
  specifications?: Record<string, string>;
  url: string;
  scrapedat: Date;
  source: EcommerceSite;
}

export interface ProductSelectors {
  title: string;
  price: string;
  originalPrice?: string;
  currency?: string;
  availability?: string;
  image?: string;
  rating?: string;
  reviewCount?: string;
  seller?: string;
  brand?: string;
  description?: string;
  features?: string;
}

export enum ProductAvailability {
  IN_STOCK = 'in_stock',
  OUT_OF_STOCK = 'out_of_stock',
  PREORDER = 'preorder',
  DISCONTINUED = 'discontinued',
  LIMITED_STOCK = 'limited_stock',
  UNKNOWN = 'unknown',
}

export enum EcommerceSite {
  AMAZON = 'amazon',
  EBAY = 'ebay',
  WALMART = 'walmart',
  TARGET = 'target',
  BESTBUY = 'bestbuy',
  ALIBABA = 'alibaba',
  FLIPKART = 'flipkart',
  SHOPIFY = 'shopify',
  UNKNOWN = 'unknown',
}
