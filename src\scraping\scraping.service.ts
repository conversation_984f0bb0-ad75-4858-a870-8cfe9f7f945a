import { Injectable } from '@nestjs/common';
import { CreateScrappingDto } from './dto/create-scrapping.dto';
import { UpdateScrappingDto } from './dto/update-scrapping.dto';

@Injectable()
export class ScrappingService {
  create(createScrappingDto: CreateScrappingDto) {
    return 'This action adds a new scrapping';
  }

  findAll() {
    return `This action returns all scrapping`;
  }

  findOne(id: number) {
    return `This action returns a #${id} scrapping`;
  }

  update(id: number, updateScrappingDto: UpdateScrappingDto) {
    return `This action updates a #${id} scrapping`;
  }

  remove(id: number) {
    return `This action removes a #${id} scrapping`;
  }
}
