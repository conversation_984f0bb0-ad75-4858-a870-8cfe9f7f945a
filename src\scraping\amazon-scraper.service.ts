import { Injectable, Logger } from '@nestjs/common';
import { BaseScraperService } from './base-scraper.service';
import {
  EcommerceSite,
  ProductAvailability,
  ProductSelectors,
  ScrapeProductData,
} from '../interfaces/scraper.interface';
import { Page } from 'puppeteer';

@Injectable()
export class AmazonScraperService extends BaseScraperService {
  protected readonly logger = new Logger(AmazonScraperService.name);

  canScrape(url: string): boolean {
    const amazonDomains = [
      'amazon.com',
      'amazon.co.uk',
      'amazon.de',
      'amazon.fr',
      'amazon.it',
      'amazon.es',
      'amazon.ca',
      'amazon.com.au',
      'amazon.in',
      'amazon.co.jp',
      'amazon.ae',
      'amazon.cn',
      'amazon.com.be',
      'amazon.com.br',
      'amazon.com.tr',
      'amazon.com.mx',
      'amazon.eg',
      'amazon.nl',
      'amazon.pl',
      'amazon.sa',
      'amazon.se',
      'amazon.sg',
      'amazon.co.za',
    ];

    try {
      const urlObj = new URL(url);
      return (
        amazonDomains.some((domain) => urlObj.hostname.includes(domain)) &&
        (url.includes('/dp/') ||
          url.includes('/gp/product') ||
          url.includes('/s?') ||
          url.includes('/s?k='))
      );
    } catch {
      return false;
    }
  }

  protected getSelectors(): ProductSelectors {
    return {
      title:
        '#productTitle, .product-title, [data-automation-id="product-title"]',
      price:
        '.a-price-whole, .a-offscreen, .a-price.a-text-price.a-size-medium.apexPriceToPay, .a-price-range',
      originalPrice:
        '.a-price.a-text-price .a-offscreen, span.a-price-base .a-offscreen',
      currency: '.a-price-symbol, .a-price-currency',
      availability:
        '#availability span, .a-size-medium.a-color-success, .a-size-medium.a-color-price, #availabilityInsideBuyBox_feature_div',
      image: '#landingImage, .a-dynamic-image, #imgTagWrapperId img',
      rating:
        '.a-icon-alt, [data-hook="average-star-rating"] .a-icon-alt, .a-star-medium .a-icon-alt',
      reviewCount:
        '#acrCustomerReviewText, [data-hook="total-review-count"], .a-size-base.a-link-normal',
      seller:
        '#sellerProfileTriggerId, .tabular-buybox-text[tabular-attribute-name="Sold by"] span, #merchant-info',
      brand:
        '.a-size-large.product-title-word-break, #bylineInfo, .a-size-base.po-brand .a-size-base',
      description:
        '#feature-bullets ul, .a-unordered-list.a-vertical.a-spacing-mini, #featurebullets_feature_div',
      features: '#feature-bullets li, .a-list-item',
    };
  }

  async scrapeProduct(
    url: string,
  ): Promise<ScrapeProductData | ScrapeProductData[]> {
    if (!this.canScrape(url)) {
      throw new Error(`URL is not valid Amazon URL: ${url}`);
    }

    let page: Page | null = null;
    const startTime = Date.now();

    try {
      page = await this.createPage();
      await page.setExtraHTTPHeaders({
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        Accept:
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
      });

      await this.navigateUrl(page, url);
      await this.handleAmazonAntiBot(page);
      await this.waitForEssentialElements(page);

      let productData: ScrapeProductData | ScrapeProductData[];

      if (this.isSearchResultsPage(url)) {
        productData = await this.extractSearchResults(page);
      } else {
        productData = await this.extractProductData(page, url);
      }

      const scrapeDuration = Date.now() - startTime;
      this.logger.log(
        `Successfully scraped Amazon product in ${scrapeDuration}ms`,
      );
      return productData;
    } catch (error) {
      this.logger.error(`Failed to scrape Amazon product: ${url}`, error.stack);
      throw error;
    } finally {
      if (page) {
        await this.closePage(page);
      }
    }
  }

  private async handleAmazonAntiBot(page: Page): Promise<void> {
    try {
      const captchaExists =
        (await page.$('#captchacharacters, .cvf-widget-form')) !== null;
      if (captchaExists) {
        this.logger.warn('Amazon Caotcha detected');
        throw new Error('Amazon Captcha detected');
      }

      const robotCheckExists =
        (await page.$('form[action*="validateCaptcha"]')) !== null;
      if (robotCheckExists) {
        this.logger.warn('Amazon Robot Check detected');
        throw new Error('Amazon Robot Check detected');
      }

      const locationModal = await page.$('[data-action="GLUXConfirmAction"]');
      if (locationModal) {
        await locationModal.click();
        await this.sleep(1000);
      }

      await this.dismissPopups(page);
      await this.simulateHumanScroll(page);
    } catch (error) {
      if (
        error instanceof Error &&
        (error.message.includes('CAPTCHA') ||
          error.message.includes('Robot check'))
      ) {
        throw error;
      }
      this.logger.debug('Error in Amazon anti-bot handling', error.message);
    }
  }

  private async dismissPopups(page: Page): Promise<void> {
    const popupSelectors = [
      '[data-action="a-popover-close"]',
      '.a-button-close',
      '[aria-label="Close"]',
      '#attach-close',
      '#nav-flyout-anchor',
      '.a-declarative[data-action="close"]',
    ];

    for (const selector of popupSelectors) {
      try {
        const popup = await page.$(selector);
        if (popup) {
          await popup.click();
          await this.sleep(1000);
        }
      } catch (error) {
        this.logger.debug('Error in popup dismissal', error.message);
      }
    }
  }

  private async waitForEssentialElements(page: Page): Promise<void> {
    try {
      if (this.isSearchResultsPage(page.url())) {
        await page.waitForSelector('.s-result-item', { timeout: 10000 });
      } else {
        const selectors = this.getSelectors();
        await this.waitForElement(page, selectors.title, 10000);
      }
    } catch (error) {
      const isProductPage = (await page.$('#dp, #ppd')) !== null;
      if (!isProductPage && !this.isSearchResultsPage(page.url())) {
        throw new Error('Not a valid Amazon product page');
      }
    }
  }

  private isSearchResultsPage(url: string): boolean {
    return url.includes('/s?') || url.includes('/s?k=');
  }

  private async extractSearchResults(page: Page): Promise<ScrapeProductData[]> {
    await page.waitForSelector('.s-result-item');

    const rawProducts = await page.$$eval('.s-result-item', (items) => {
      return items
        .map((el) => {
          const title = el.querySelector('h2 a span')?.textContent?.trim();
          const link = el.querySelector('h2 a')?.getAttribute('href');
          const imageUrl = el.querySelector('img')?.getAttribute('src');
          const priceWhole = el
            .querySelector('.a-price .a-price-whole')
            ?.textContent?.replace(/[^\d]/g, '');
          const priceFraction = el
            .querySelector('.a-price .a-price-fraction')
            ?.textContent?.replace(/[^\d]/g, '');
          const rating = el.querySelector('.a-icon-alt')?.textContent?.trim();
          const reviewCount = el
            .querySelector('.a-size-base.s-underline-text')
            ?.textContent?.trim();

          if (!title || !link || !priceWhole) return null;

          return {
            title,
            url: 'https://www.amazon.com' + link,
            imageUrl,
            price: `${priceWhole}.${priceFraction || '00'}`,
            rating,
            reviewCount,
          };
        })
        .filter(Boolean);
    });

    const products: ScrapeProductData[] = rawProducts.map((item) => ({
      title: item?.title,
      price: item?.price ? parseFloat(item.price) : null,
      originalPrice: null,
      currency: 'USD',
      availability: ProductAvailability.UNKNOWN,
      imageUrl: item?.imageUrl,
      rating: item?.rating,
      reviewCount: item?.reviewCount,
      seller: null,
      brand: null,
      description: null,
      features: [],
      specifications: {},
      url: item?.url,
      scrapedat: new Date(),
      source: EcommerceSite.AMAZON,
    }));

    this.logger.log(`Extracted ${products.length} products from search page`);
    return products;
  }

  private async extractProductData(
    page: Page,
    url: string,
  ): Promise<ScrapeProductData> {
    const selectors = this.getSelectors();

    // Extract basic product information
    const title = await this.extractTitle(page, selectors.title);
    const { price, currency } = await this.extractPriceInfo(page, selectors);
    const originalPrice = await this.extractOriginalPrice(
      page,
      selectors.originalPrice as string,
    );
    const availability = await this.extractAvailability(
      page,
      selectors.availability as string,
    );
    const imageUrl = await this.extractImageUrl(
      page,
      selectors.image as string,
    );

    // Extract additional information
    const rating = await this.extractRating(page, selectors.rating as string);
    const reviewCount = await this.extractReviewCount(
      page,
      selectors.reviewCount as string,
    );
    const seller = await this.extractSeller(page, selectors.seller as string);
    const brand = await this.extractBrand(page, selectors.brand as string);
    const description = await this.extractDescription(
      page,
      selectors.description as string,
    );
    const features = await this.extractFeatures(
      page,
      selectors.features as string,
    );
    const specifications = await this.extractSpecifications(page);

    return {
      title: title || 'Unknown Product',
      price,
      originalPrice,
      currency: currency || 'USD',
      availability,
      imageUrl,
      rating,
      reviewCount,
      seller,
      brand,
      description,
      features,
      specifications,
      url,
      scrapedat: new Date(),
      source: EcommerceSite.AMAZON,
    };
  }

  private async extractTitle(page: Page, selector: string): Promise<string> {
    const rawTitle = await this.extractText(page, selector);
    return rawTitle ? this.cleanTitle(rawTitle) : '';
  }

  private async extractPriceInfo(
    page: Page,
    selectors: ProductSelectors,
  ): Promise<{ price: number | null; currency: string }> {
    // Try multiple price selectors
    const priceSelectors = [
      '.a-price-whole',
      '.a-offscreen',
      '.a-price.a-text-price.a-size-medium.apexPriceToPay .a-offscreen',
      '.a-price-range .a-offscreen',
    ];

    let priceText: string | null = '';
    let currency = 'USD';

    for (const selector of priceSelectors) {
      priceText = await this.extractText(page, selector);
      if (priceText) break;
    }

    const currencyText = await this.extractText(
      page,
      selectors.currency as string,
    );
    if (currencyText) {
      currency = this.parseCurrency(currencyText);
    }
    if (priceText && /^[\$£€¥₹]/.test(priceText)) {
      currency = this.parseCurrency(priceText.charAt(0));
      priceText = priceText.substring(1);
    }

    const price = this.parsePrice(priceText as string);
    return { price, currency };
  }

  private async extractOriginalPrice(
    page: Page,
    selector: string,
  ): Promise<number | null> {
    const originalPriceText = await this.extractText(page, selector);
    return this.parsePrice(originalPriceText as string);
  }

  private parseCurrency(symbol: string): string {
    const currencyMap: Record<string, string> = {
      $: 'USD',
      '£': 'GBP',
      '€': 'EUR',
      '¥': 'JPY',
      '₹': 'INR',
      C$: 'CAD',
      A$: 'AUD',
    };

    return currencyMap[symbol] || 'USD';
  }

  private async extractAvailability(
    page: Page,
    selector: string,
  ): Promise<ProductAvailability> {
    const availabilityText = await this.extractText(page, selector);

    if (!availabilityText) {
      return ProductAvailability.UNKNOWN;
    }

    const text = availabilityText.toLowerCase();

    if (text.includes('in stock') || text.includes('available')) {
      return ProductAvailability.IN_STOCK;
    }
    if (text.includes('out of stock') || text.includes('unavailable')) {
      return ProductAvailability.OUT_OF_STOCK;
    }
    if (text.includes('only') && text.includes('left')) {
      return ProductAvailability.LIMITED_STOCK;
    }
    if (text.includes('pre-order') || text.includes('preorder')) {
      return ProductAvailability.PREORDER;
    }

    return ProductAvailability.UNKNOWN;
  }

  private async extractImageUrl(
    page: Page,
    selector: string,
  ): Promise<string | undefined> {
    const imageUrl =
      (await this.extractAttribute(page, selector, 'src')) ||
      (await this.extractAttribute(page, selector, 'data-src'));

    return imageUrl || undefined;
  }

  private async extractRating(
    page: Page,
    selector: string,
  ): Promise<number | undefined> {
    const ratingText = await this.extractText(page, selector);
    if (!ratingText) return undefined;

    const ratingMatch = ratingText.match(/(\d+\.?\d*)\s*out\s*of\s*5/i);
    if (ratingMatch) {
      return parseFloat(ratingMatch[1]);
    }

    return undefined;
  }

  private async extractReviewCount(
    page: Page,
    selector: string,
  ): Promise<number | undefined> {
    const reviewText = await this.extractText(page, selector);
    if (!reviewText) return undefined;

    const reviewMatch = reviewText.match(/(\d+(?:,\d+)*)/);
    if (reviewMatch) {
      return parseInt(reviewMatch[1].replace(/,/g, ''), 10);
    }

    return undefined;
  }

  private async extractSeller(
    page: Page,
    selector: string,
  ): Promise<string | undefined> {
    const sellerText = await this.extractText(page, selector);
    return sellerText?.trim() || undefined;
  }

  private async extractBrand(
    page: Page,
    selector: string,
  ): Promise<string | undefined> {
    const brandText = await this.extractText(page, selector);

    if (brandText) {
      // Clean brand text (remove "Brand: " prefix if present)
      return brandText.replace(/^(Brand:\s*|Visit the\s*|by\s*)/i, '').trim();
    }

    return undefined;
  }

  private async extractDescription(
    page: Page,
    selector: string,
  ): Promise<string | undefined> {
    try {
      const descriptionElement = await page.$(selector);
      if (!descriptionElement) return undefined;

      const description = await page.evaluate((el) => {
        const items = el.querySelectorAll('li');
        return Array.from(items)
          .map((item) => item.textContent?.trim())
          .filter((text) => text && text.length > 0)
          .join('. ');
      }, descriptionElement);

      return description || undefined;
    } catch (error) {
      this.logger.debug('Error extracting description:', error.message);
      return undefined;
    }
  }

  private async extractFeatures(
    page: Page,
    selector: string,
  ): Promise<string[] | undefined> {
    try {
      const features = await page.evaluate((sel) => {
        const elements = document.querySelectorAll(sel);
        const featureList: string[] = [];

        elements.forEach((el) => {
          const text = el.textContent?.trim();
          if (text && text.length > 10 && !text.includes('See more')) {
            featureList.push(text);
          }
        });

        return featureList.slice(0, 10); // Limit to 10 features
      }, selector);

      return features.length > 0 ? features : undefined;
    } catch (error) {
      this.logger.debug('Error extracting features:', error.message);
      return undefined;
    }
  }

  private async extractSpecifications(
    page: Page,
  ): Promise<Record<string, string> | undefined> {
    try {
      const specs = await page.evaluate(() => {
        const specTable = document.querySelector(
          '#productDetails_techSpec_section_1, .pdTab',
        );
        if (!specTable) return {};

        const specifications: Record<string, string> = {};
        const rows = specTable.querySelectorAll('tr');

        rows.forEach((row) => {
          const cells = row.querySelectorAll('td, th');
          if (cells.length >= 2) {
            const key = cells[0].textContent?.trim();
            const value = cells[1].textContent?.trim();
            if (key && value) {
              specifications[key] = value;
            }
          }
        });

        return specifications;
      });

      return Object.keys(specs).length > 0 ? specs : undefined;
    } catch (error) {
      this.logger.debug('Error extracting specifications:', error.message);
      return undefined;
    }
  }
}
